#include "DruidsSageSettings.h"
#include "LogDruids.h"
#include <Runtime/Launch/Resources/Version.h>

#ifdef UE_INLINE_GENERATED_CPP_BY_NAME
#include UE_INLINE_GENERATED_CPP_BY_NAME(DruidsSageSettings)
#endif

UDruidsSageSettings::UDruidsSageSettings(const FObjectInitializer& ObjectInitializer) :
	Super(ObjectInitializer), bEnableInternalLogs(false)
{
	CategoryName = TEXT("Plugins");
}

const UDruidsSageSettings* UDruidsSageSettings::Get()
{
	const UDruidsSageSettings* const Instance = GetDefault<UDruidsSageSettings>();
	return Instance;
}

#if WITH_EDITOR
void UDruidsSageSettings::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
	Super::PostEditChangeProperty(PropertyChangedEvent);

	if (PropertyChangedEvent.Property->GetFName() == GET_MEMBER_NAME_CHECKED(UDruidsSageSettings, bEnableInternalLogs))
	{
		ToggleInternalLogs();
	}
}
#endif

void UDruidsSageSettings::PostInitProperties()
{
	Super::PostInitProperties();
	ToggleInternalLogs();
}

void UDruidsSageSettings::SaveAndReload(const FName& PropertyName)
{
	SaveConfig();

	uint32 PropagationFlags = 0u;

#if ENGINE_MAJOR_VERSION >= 5
	PropagationFlags = UE::ELoadConfigPropagationFlags::LCPF_PropagateToChildDefaultObjects;
#else
    PropagationFlags = UE4::ELoadConfigPropagationFlags::LCPF_PropagateToChildDefaultObjects;
#endif

	ReloadConfig(GetClass(), *GetDefaultConfigFilename(), PropagationFlags, GetClass()->FindPropertyByName(PropertyName));
}

void UDruidsSageSettings::ToggleInternalLogs()
{
#if !UE_BUILD_SHIPPING
	LogDruidsSage_Internal.SetVerbosity(bEnableInternalLogs ? ELogVerbosity::Display : ELogVerbosity::NoLogging);
#endif
}
