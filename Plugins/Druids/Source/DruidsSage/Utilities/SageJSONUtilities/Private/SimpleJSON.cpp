#include "SimpleJSON.h"

#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "Policies/CondensedJsonPrintPolicy.h"

void SimpleJSON::EnsureValid()
{
    if (!JsonValue.IsValid())
    {
        JsonValue = MakeShareable(new FJsonValueObject(MakeShareable(new FJsonObject)));
        UpdateParent();
    }
}

void SimpleJSON::UpdateParent()
{
    if (IsObjectField() && JsonValue.IsValid())
    {
        ParentObject->SetField(ParentKey, JsonValue);
    }
    else if (IsArrayElement() && JsonValue.IsValid() && ParentArrayValue.IsValid())
    {
        TArray<TSharedPtr<FJsonValue>>* Array;
        ParentArrayValue->TryGetArray(Array);
        if (Array->IsValidIndex(ArrayIndex))
        {
            (*Array)[ArrayIndex] = JsonValue;
        }
    }
}

void SimpleJSON::EnsureObject()
{
    if (!JsonValue.IsValid() || JsonValue->Type != EJson::Object)
    {
        JsonValue = MakeShareable(new FJsonValueObject(MakeShareable(new FJsonObject)));
        UpdateParent();
    }
}

void SimpleJSON::EnsureArray()
{
    if (!JsonValue.IsValid() || JsonValue->Type != EJson::Array)
    {
        JsonValue = MakeShareable(new FJsonValueArray(TArray<TSharedPtr<FJsonValue>>()));
        UpdateParent();
    }
}

SimpleJSON::SimpleJSON(const FJsonValue& InValue): ArrayIndex(-1)
{
    // Create a deep copy
    switch (InValue.Type)
    {
    case EJson::None:
    case EJson::Null:
        JsonValue = MakeShareable(new FJsonValueNull());
        break;
    case EJson::String:
        JsonValue = MakeShareable(new FJsonValueString(InValue.AsString()));
        break;
    case EJson::Number:
        JsonValue = MakeShareable(new FJsonValueNumber(InValue.AsNumber()));
        break;
    case EJson::Boolean:
        JsonValue = MakeShareable(new FJsonValueBoolean(InValue.AsBool()));
        break;
    case EJson::Array:
        JsonValue = MakeShareable(new FJsonValueArray(InValue.AsArray()));
        break;
    case EJson::Object:
        JsonValue = MakeShareable(new FJsonValueObject(InValue.AsObject()));
        break;
    }
}

SimpleJSON::SimpleJSON(const FString& JsonString): ArrayIndex(-1)
{
    TSharedPtr<FJsonValue> ParsedValue;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);
        
    if (FJsonSerializer::Deserialize(Reader, ParsedValue) && ParsedValue.IsValid())
    {
        JsonValue = ParsedValue;
    }
    else
    {
        // Default to empty object on parse failure
        JsonValue = MakeShareable(new FJsonValueObject(MakeShareable(new FJsonObject)));
    }
}

SimpleJSON SimpleJSON::Parse(const FString& JsonString)
{
    return SimpleJSON(JsonString);
}

SimpleJSON SimpleJSON::FromFile(const FString& FilePath)
{
    FString JsonString;
    if (FFileHelper::LoadFileToString(JsonString, *FilePath))
    {
        return Parse(JsonString);
    }
    return SimpleJSON();
}

SimpleJSON SimpleJSON::Array()
{
    SimpleJSON Result;
    Result.EnsureArray();
    return Result;
}

SimpleJSON SimpleJSON::Object()
{
    return SimpleJSON();
}


SimpleJSON SimpleJSON::operator[](const FString& Key)
{
    EnsureObject();

    TSharedPtr<FJsonObject> Object = JsonValue->AsObject();

    TSharedPtr<FJsonValue> FieldValue;
    if (Object->HasField(Key))
    {
        FieldValue = Object->GetField<EJson::None>(Key);
    }
    else
    {
        // Create new empty object for the key
        FieldValue = MakeShareable(new FJsonValueObject(MakeShareable(new FJsonObject)));
        Object->SetField(Key, FieldValue);
    }

    // Return a SimpleJSON that knows it's a field in this object
    return SimpleJSON(FieldValue, Object, Key);
}

SimpleJSON SimpleJSON::operator[](int32 Index)
{
    EnsureArray();

    TArray<TSharedPtr<FJsonValue>>* Array;
    if (!JsonValue->TryGetArray(Array))
    {
        return SimpleJSON();
    }
        
    // Expand array if needed
    while (Array->Num() <= Index)
    {
        Array->Add(MakeShareable(new FJsonValueObject(MakeShareable(new FJsonObject))));
    }
        
    // Return a SimpleJSON that knows it's an element in this array
    return SimpleJSON((*Array)[Index], JsonValue, Index);
}

SimpleJSON& SimpleJSON::operator=(const FString& Value)
{
    JsonValue = MakeShareable(new FJsonValueString(Value));
    UpdateParent();
    return *this;
}

SimpleJSON& SimpleJSON::operator=(const TCHAR* Value)
{
    JsonValue = MakeShareable(new FJsonValueString(Value));
    UpdateParent();
    return *this;
}

SimpleJSON& SimpleJSON::operator=(int32 Value)
{
    JsonValue = MakeShareable(new FJsonValueNumber(Value));
    UpdateParent();
    return *this;
}

SimpleJSON& SimpleJSON::operator=(float Value)
{
    JsonValue = MakeShareable(new FJsonValueNumber(Value));
    UpdateParent();
    return *this;
}

SimpleJSON& SimpleJSON::operator=(double Value)
{
    JsonValue = MakeShareable(new FJsonValueNumber(Value));
    UpdateParent();
    return *this;
}

SimpleJSON& SimpleJSON::operator=(bool Value)
{
    JsonValue = MakeShareable(new FJsonValueBoolean(Value));
    UpdateParent();
    return *this;
}

SimpleJSON& SimpleJSON::operator=(const TArray<SimpleJSON>& Array)
{
    TArray<TSharedPtr<FJsonValue>> JsonArray;
    for (const SimpleJSON& Item : Array)
    {
        JsonArray.Add(Item.JsonValue);
    }

    JsonValue = MakeShareable(new FJsonValueArray(JsonArray));
    UpdateParent();
    return *this;
}

SimpleJSON& SimpleJSON::operator=(const SimpleJSON& Other)
{
    JsonValue = Other.JsonValue;
    UpdateParent();
    return *this;
}

SimpleJSON& SimpleJSON::SetNull()
{
    JsonValue = MakeShareable(new FJsonValueNull);
    UpdateParent();
    return *this;
}

bool SimpleJSON::IsString() const
{
    return JsonValue.IsValid() && JsonValue->Type == EJson::String;
}

bool SimpleJSON::IsNumber() const
{
    return JsonValue.IsValid() && JsonValue->Type == EJson::Number;
}

bool SimpleJSON::IsBool() const
{
    return JsonValue.IsValid() && JsonValue->Type == EJson::Boolean;
}

bool SimpleJSON::IsArray() const
{
    return JsonValue.IsValid() && JsonValue->Type == EJson::Array;
}

bool SimpleJSON::IsObject() const
{
    return JsonValue.IsValid() && JsonValue->Type == EJson::Object;
}

bool SimpleJSON::IsNull() const
{
    return JsonValue.IsValid() && JsonValue->Type == EJson::Null;
}

bool SimpleJSON::IsValid() const
{
    return JsonValue.IsValid();
}

bool SimpleJSON::IsEmpty() const
{
    if (IsArray())
    {
        return AsArray().IsEmpty();
    }

    //If it is not an array then it is an "empty" array
    return true;
}

FString SimpleJSON::AsString(const FString& DefaultValue) const
{
    if (JsonValue.IsValid() && JsonValue->Type == EJson::String)
    {
        return JsonValue->AsString();
    }
    return DefaultValue;
}

int32 SimpleJSON::AsInt(int32 DefaultValue) const
{
    if (JsonValue.IsValid() && JsonValue->Type == EJson::Number)
    {
        return FMath::TruncToInt(JsonValue->AsNumber());
    }
    return DefaultValue;
}

float SimpleJSON::AsFloat(float DefaultValue) const
{
    if (JsonValue.IsValid() && JsonValue->Type == EJson::Number)
    {
        return JsonValue->AsNumber();
    }
    return DefaultValue;
}

double SimpleJSON::AsDouble(double DefaultValue) const
{
    if (JsonValue.IsValid() && JsonValue->Type == EJson::Number)
    {
        return JsonValue->AsNumber();
    }
    return DefaultValue;
}

bool SimpleJSON::AsBool(bool DefaultValue) const
{
    if (JsonValue.IsValid() && JsonValue->Type == EJson::Boolean)
    {
        return JsonValue->AsBool();
    }
    return DefaultValue;
}

TArray<SimpleJSON> SimpleJSON::AsArray() const
{
    TArray<SimpleJSON> Result;
    if (IsArray())
    {
        const TArray<TSharedPtr<FJsonValue>>& Array = JsonValue->AsArray();
        for (const auto& Item : Array)
        {
            Result.Add(SimpleJSON(Item));
        }
    }

    return Result;
}

TArray<TSharedPtr<FJsonValue>> SimpleJSON::AsNativeArray() const
{
    TArray<TSharedPtr<FJsonValue>> Result;
    if (IsArray())
    {
        const TArray<TSharedPtr<FJsonValue>>& Array = JsonValue->AsArray();
        for (const auto& Item : Array)
        {
            Result.Add(Item);
        }
    }

    return Result;
}

bool SimpleJSON::HasKey(const FString& Key) const
{
    if (IsObject())
    {
        return JsonValue->AsObject()->HasField(Key);
    }
    return false;
}

TArray<FString> SimpleJSON::GetKeys() const
{
    TArray<FString> Keys;
    if (IsObject())
    {
        JsonValue->AsObject()->Values.GetKeys(Keys);
    }
    return Keys;
}

bool SimpleJSON::RemoveKey(const FString& Key) const
{
    if (IsObject())
    {
        if (TSharedPtr<FJsonObject> JsonObject = JsonValue->AsObject(); JsonObject.IsValid())
        {
            if (JsonObject->HasField(Key))
            {
                JsonObject->RemoveField(Key);
                return true;
            }
        }
    }

    return false;
}

int32 SimpleJSON::Size() const
{
    if (JsonValue.IsValid() && JsonValue->Type == EJson::Array)
    {
        return JsonValue->AsArray().Num();
    }
    else if (JsonValue.IsValid() && JsonValue->Type == EJson::Object)
    {
        return JsonValue->AsObject()->Values.Num();
    }
    return 0;
}

void SimpleJSON::Push(const SimpleJSON& Item)
{
    EnsureArray();
    if (TArray<TSharedPtr<FJsonValue>>* Array; JsonValue->TryGetArray(Array))
    {
        Array->Add(Item.JsonValue);
    }
}

void SimpleJSON::Push(const FString& Item)
{
    Push(SimpleJSON().SetValue(Item));
}

void SimpleJSON::Push(const int32 Item)
{
    Push(SimpleJSON().SetValue(Item));
}

void SimpleJSON::Push(const float Item)
{
    Push(SimpleJSON().SetValue(Item));
}

void SimpleJSON::Push(const bool Item)
{
    Push(SimpleJSON().SetValue(Item));
}

SimpleJSON& SimpleJSON::SetValue(const FString& Value)
{
    *this = Value;
    return *this;
}

SimpleJSON& SimpleJSON::SetValue(const int32 Value)
{
    *this = Value;
    return *this;
}

SimpleJSON& SimpleJSON::SetValue(const float Value)
{
    *this = Value;
    return *this;
}

SimpleJSON& SimpleJSON::SetValue(const bool Value)
{
    *this = Value;
    return *this;
}

bool SimpleJSON::RemoveAt(const int32 Index) const
{
    if (IsArray())
    {
        if (TArray<TSharedPtr<FJsonValue>>* Array; JsonValue->TryGetArray(Array))
        {
            if (Array->IsValidIndex(Index))
            {
                Array->RemoveAt(Index);
                return true;
            }
        }
    }
    return false;
}

void SimpleJSON::Clear() const
{
    if (IsArray())
    {
        if (TArray<TSharedPtr<FJsonValue>>* Array; JsonValue->TryGetArray(Array))
        {
            Array->Empty();
        }
    }
    else if (IsObject())
    {
        JsonValue->AsObject()->Values.Empty();
    }
}

FString SimpleJSON::ToString(const bool bPrettyPrint) const
{
    FString OutputString;
    if (JsonValue.IsValid())
    {
        if (bPrettyPrint)
        {
            TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
            FJsonSerializer::Serialize(JsonValue->AsObject().ToSharedRef(), Writer, true);
        }
        else
        {
            TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> Writer =
                TJsonWriterFactory<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>::Create(&OutputString);
            FJsonSerializer::Serialize(JsonValue->AsObject().ToSharedRef(), Writer, true);
        }
    }
    return OutputString;
}

bool SimpleJSON::SaveToFile(const FString& FilePath, bool bPrettyPrint) const
{
    FString JsonString = ToString(bPrettyPrint);
    return FFileHelper::SaveStringToFile(JsonString, *FilePath);
}

TSharedPtr<FJsonObject> SimpleJSON::GetJsonObject() const
{
    if (IsObject())
    {
        return JsonValue->AsObject();
    }

    return nullptr;
}

SimpleJSON::Iterator::Iterator(const TMap<FString, TSharedPtr<FJsonValue>>& Map, bool bBegin)
    : bIsMapIterator(true)
{
    if (bBegin)
    {
        MapIter = MakeUnique<MapIteratorType>(Map.CreateConstIterator());
    }
}

SimpleJSON::Iterator::Iterator(const TArray<TSharedPtr<FJsonValue>>& Array, bool bBegin)
    : bIsMapIterator(false)
{
    if (bBegin)
    {
        ArrayIter = MakeUnique<ArrayIteratorType>(Array.CreateConstIterator());
    }
}

SimpleJSON SimpleJSON::Iterator::operator*() const
{
    if (bIsMapIterator && MapIter.IsValid())
    {
        return SimpleJSON((*MapIter)->Value);
    }
    else if (!bIsMapIterator && ArrayIter.IsValid())
    {
        return SimpleJSON(**ArrayIter);
    }
    return SimpleJSON();
}

SimpleJSON::Iterator& SimpleJSON::Iterator::operator++()
{
    if (bIsMapIterator && MapIter.IsValid())
    {
        ++(*MapIter);
    }
    else if (!bIsMapIterator && ArrayIter.IsValid())
    {
        ++(*ArrayIter);
    }
    return *this;
}

bool SimpleJSON::Iterator::operator!=(const Iterator& Other) const
{
    if (bIsMapIterator != Other.bIsMapIterator) return true;
    if (bIsMapIterator)
    {
        if (MapIter.IsValid() != Other.MapIter.IsValid()) return true;
        if (MapIter.IsValid() && Other.MapIter.IsValid())
        {
            return *MapIter != *Other.MapIter;
        }
        return false;
    }
    else
    {
        if (ArrayIter.IsValid() != Other.ArrayIter.IsValid()) return true;
        if (ArrayIter.IsValid() && Other.ArrayIter.IsValid())
        {
            return *ArrayIter != *Other.ArrayIter;
        }
        return false;
    }
}

SimpleJSON::Iterator SimpleJSON::begin() const
{
    if (JsonValue.IsValid() && JsonValue->Type == EJson::Array)
    {
        return Iterator(JsonValue->AsArray(), true);
    }
    else if (JsonValue.IsValid() && JsonValue->Type == EJson::Object)
    {
        return Iterator(JsonValue->AsObject()->Values, true);
    }
    static TMap<FString, TSharedPtr<FJsonValue>> EmptyMap;
    return Iterator(EmptyMap, false);
}

SimpleJSON::Iterator SimpleJSON::end() const
{
    if (JsonValue.IsValid() && JsonValue->Type == EJson::Array)
    {
        return Iterator(JsonValue->AsArray(), false);
    }
    else if (JsonValue.IsValid() && JsonValue->Type == EJson::Object)
    {
        return Iterator(JsonValue->AsObject()->Values, false);
    }
    static TMap<FString, TSharedPtr<FJsonValue>> EmptyMap;
    return Iterator(EmptyMap, false);
}