#include "ChatWidgetOverrides.h"
#include "DruidsSageChatShell.h"
#include "DruidsSageSimpleChatItem.h"
#include "DruidsSageActionRequestChatItem.h"
#include "DruidsSageAssistantChatItem.h"

UChatWidgetOverrides::UChatWidgetOverrides()
{
	// Initialize with nullptr - will use base classes by default
	DefaultChatShellClass = nullptr;
	DefaultSimpleChatItemClass = nullptr;
	DefaultActionRequestChatItemClass = nullptr;
	DefaultAssistantChatItemClass = nullptr;
	DefaultAssistantTextChatItemClass = nullptr;
}

TSubclassOf<UDruidsSageChatShell> UChatWidgetOverrides::GetChatShellWidgetClass_Implementation() const
{
	// Return the default class if set, otherwise nullptr (which means use base class)
	return DefaultChatShellClass;
}

TSubclassOf<UDruidsSageSimpleChatItem> UChatWidgetOverrides::GetSimpleChatItemWidgetClass_Implementation() const
{
	// Return the default class if set, otherwise nullptr (which means use base class)
	return DefaultSimpleChatItemClass;
}

TSubclassOf<UDruidsSageActionRequestChatItem> UChatWidgetOverrides::GetActionRequestChatItemWidgetClass_Implementation() const
{
	// Return the default class if set, otherwise nullptr (which means use base class)
	return DefaultActionRequestChatItemClass;
}

TSubclassOf<UDruidsSageAssistantChatItem> UChatWidgetOverrides::GetAssistantChatItemWidgetClass_Implementation() const
{
	// Return the default class if set, otherwise nullptr (which means use base class)
	return DefaultAssistantChatItemClass;
}

TSubclassOf<UDruidsSageAssistantTextChatItem> UChatWidgetOverrides::GetAssistantTextChatItemWidgetClass_Implementation() const
{
	// Return the default class if set, otherwise nullptr (which means use base class)
	return DefaultAssistantTextChatItemClass;
}
