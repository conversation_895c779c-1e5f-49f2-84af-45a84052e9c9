#pragma once

#include "CoreMinimal.h"
#include "Engine/Blueprint.h"
#include "DruidsSageChatShell.h"
#include "ChatWidgetOverrides.generated.h"

// Forward declarations for chat item classes
class UDruidsSageSimpleChatItem;
class UDruidsSageActionRequestChatItem;
class UDruidsSageAssistantChatItem;

/**
 * ChatWidgetOverrides class that can be overridden in Blueprint to specify
 * which UDruidsSageChatShell class to instantiate instead of the base class.
 * This allows for Blueprint customization of the chat shell widget.
 */
UCLASS(Blueprintable, BlueprintType)
class SAGEUI_API UChatWidgetOverrides : public UObject
{
	GENERATED_BODY()

public:
	UChatWidgetOverrides();

	/**
	 * Gets the widget class to instantiate for the chat shell.
	 * Can be overridden in Blueprint to return a custom UDruidsSageChatShell subclass.
	 * @return The UDruidsSageChatShell class to instantiate, or nullptr to use the base class
	 */
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageChatShell> GetChatShellWidgetClass() const;

	/**
	 * Gets the widget class to instantiate for simple chat items.
	 * Can be overridden in Blueprint to return a custom UDruidsSageSimpleChatItem subclass.
	 * @return The UDruidsSageSimpleChatItem class to instantiate, or nullptr to use the base class
	 */
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageSimpleChatItem> GetSimpleChatItemWidgetClass() const;

	/**
	 * Gets the widget class to instantiate for action request chat items.
	 * Can be overridden in Blueprint to return a custom UDruidsSageActionRequestChatItem subclass.
	 * @return The UDruidsSageActionRequestChatItem class to instantiate, or nullptr to use the base class
	 */
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageActionRequestChatItem> GetActionRequestChatItemWidgetClass() const;

	/**
	 * Gets the widget class to instantiate for assistant chat items.
	 * Can be overridden in Blueprint to return a custom UDruidsSageAssistantChatItem subclass.
	 * @return The UDruidsSageAssistantChatItem class to instantiate, or nullptr to use the base class
	 */
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageAssistantChatItem> GetAssistantChatItemWidgetClass() const;

	/**
	 * Gets the widget class to instantiate for an assistant text chat item
	 * Can be overridden in Blueprint to return a custom UDruidsSageAssistantTextChatItem subclass.
	 * @return The UDruidsSageAssistantTextChatItem class to instantiate, or nullptr to use the base class
	 */
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Chat Widget Overrrides")
	TSubclassOf<UDruidsSageAssistantTextChatItem> GetAssistantTextChatItemWidgetClass() const;

	/**
	 * C++ implementation that returns the base class by default.
	 * Blueprint implementations should override GetChatShellWidgetClass instead.
	 */
	virtual TSubclassOf<UDruidsSageChatShell> GetChatShellWidgetClass_Implementation() const;

	/**
	 * C++ implementation that returns the base class by default.
	 * Blueprint implementations should override GetSimpleChatItemWidgetClass instead.
	 */
	virtual TSubclassOf<UDruidsSageSimpleChatItem> GetSimpleChatItemWidgetClass_Implementation() const;

	/**
	 * C++ implementation that returns the base class by default.
	 * Blueprint implementations should override GetActionRequestChatItemWidgetClass instead.
	 */
	virtual TSubclassOf<UDruidsSageActionRequestChatItem> GetActionRequestChatItemWidgetClass_Implementation() const;

	/**
	 * C++ implementation that returns the base class by default.
	 * Blueprint implementations should override GetAssistantChatItemWidgetClass instead.
	 */
	virtual TSubclassOf<UDruidsSageAssistantChatItem> GetAssistantChatItemWidgetClass_Implementation() const;

	/**
	 * C++ implementation that returns the base class by default.
	 * Blueprint implementations should override GetAssistantTextChatItemWidgetClass instead.
	 */
	virtual TSubclassOf<UDruidsSageAssistantTextChatItem> GetAssistantTextChatItemWidgetClass_Implementation() const;

protected:
	/**
	 * The default chat shell widget class to use if no override is specified.
	 * This can be set in Blueprint or left as nullptr to use UDruidsSageChatShell.
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageChatShell> DefaultChatShellClass;

	/**
	 * The default simple chat item widget class to use if no override is specified.
	 * This can be set in Blueprint or left as nullptr to use UDruidsSageSimpleChatItem.
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageSimpleChatItem> DefaultSimpleChatItemClass;

	/**
	 * The default action request chat item widget class to use if no override is specified.
	 * This can be set in Blueprint or left as nullptr to use UDruidsSageActionRequestChatItem.
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageActionRequestChatItem> DefaultActionRequestChatItemClass;

	/**
	 * The default assistant chat item widget class to use if no override is specified.
	 * This can be set in Blueprint or left as nullptr to use UDruidsSageAssistantChatItem.
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageAssistantChatItem> DefaultAssistantChatItemClass;

	/**
	 * The default assistant chat item widget class to use if no override is specified.
	 * This can be set in Blueprint or left as nullptr to use UDruidsSageAssistantTextChatItem.
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageAssistantTextChatItem> DefaultAssistantTextChatItemClass;
};
